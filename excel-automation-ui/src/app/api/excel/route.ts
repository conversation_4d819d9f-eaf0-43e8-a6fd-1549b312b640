import { NextRequest, NextResponse } from 'next/server';
import * as XLSX from 'xlsx';
import path from 'path';
import fs from 'fs';

interface ExcelRow {
  id: number;
  [key: string]: any;
}

interface FilterOptions {
  search?: string;
  mappingStatus?: string;
  tags?: string[];
  page?: number;
  pageSize?: number;
}

let cachedData: ExcelRow[] | null = null;
let allTags: Set<string> = new Set();

function loadExcelData(): ExcelRow[] {
  if (cachedData) return cachedData;

  try {
    const filePath = path.join(process.cwd(), 'Summit_ExpereMapping_v15 02.08.2024 (6).xlsx');

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      console.error('File not found at:', filePath);
      return [];
    }

    console.log('Reading file from:', filePath);

    // Read file as buffer first
    const fileBuffer = fs.readFileSync(filePath);
    const workbook = XLSX.read(fileBuffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const jsonData = XLSX.utils.sheet_to_json(worksheet);

    cachedData = jsonData.map((row: any, index: number) => ({
      id: index + 1,
      ...row
    }));

    // Extract all unique tags from relevant columns
    const tagColumns = [
      'Auto Selection Packages',
      'Documents', 
      'Auto Selection Documents'
    ];

    cachedData.forEach(row => {
      tagColumns.forEach(col => {
        if (row[col] && typeof row[col] === 'string') {
          const tags = row[col].split('\n').filter((tag: string) => tag.trim());
          tags.forEach((tag: string) => allTags.add(tag.trim()));
        }
      });
    });

    return cachedData;
  } catch (error) {
    console.error('Error loading Excel file:', error);
    return [];
  }
}

function filterData(data: ExcelRow[], filters: FilterOptions): ExcelRow[] {
  let filtered = [...data];

  // Search filter
  if (filters.search) {
    const searchTerm = filters.search.toLowerCase();
    filtered = filtered.filter(row => 
      Object.values(row).some(value => 
        value && value.toString().toLowerCase().includes(searchTerm)
      )
    );
  }

  // Mapping status filter
  if (filters.mappingStatus) {
    filtered = filtered.filter(row => 
      row['Mapping Status'] === filters.mappingStatus
    );
  }

  // Tags filter
  if (filters.tags && filters.tags.length > 0) {
    filtered = filtered.filter(row => {
      const tagColumns = ['Auto Selection Packages', 'Documents', 'Auto Selection Documents'];
      return tagColumns.some(col => {
        if (row[col] && typeof row[col] === 'string') {
          const rowTags = row[col].split('\n').map((tag: string) => tag.trim());
          return filters.tags!.some(filterTag => 
            rowTags.some(rowTag => rowTag.includes(filterTag))
          );
        }
        return false;
      });
    });
  }

  return filtered;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const mappingStatus = searchParams.get('mappingStatus') || '';
    const tags = searchParams.get('tags')?.split(',').filter(Boolean) || [];
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '50');

    const data = loadExcelData();
    const filtered = filterData(data, { search, mappingStatus, tags });
    
    // Pagination
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedData = filtered.slice(startIndex, endIndex);

    return NextResponse.json({
      data: paginatedData,
      total: filtered.length,
      page,
      pageSize,
      totalPages: Math.ceil(filtered.length / pageSize)
    });
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json({ error: 'Failed to process data' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, data: requestData } = body;

    if (action === 'getTags') {
      const data = loadExcelData();
      return NextResponse.json({ tags: Array.from(allTags).sort() });
    }

    if (action === 'getColumns') {
      const data = loadExcelData();
      if (data.length > 0) {
        const columns = Object.keys(data[0]).filter(key => key !== 'id');
        return NextResponse.json({ columns });
      }
      return NextResponse.json({ columns: [] });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json({ error: 'Failed to process request' }, { status: 500 });
  }
}
