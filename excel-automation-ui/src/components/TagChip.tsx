'use client';

import { X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TagChipProps {
  tag: string;
  isSelected?: boolean;
  isInteractive?: boolean;
  onSelect?: (tag: string) => void;
  onRemove?: (tag: string) => void;
  variant?: 'default' | 'mapped' | 'filter';
}

export function TagChip({ 
  tag, 
  isSelected = false, 
  isInteractive = true,
  onSelect, 
  onRemove,
  variant = 'default'
}: TagChipProps) {
  const baseClasses = "inline-flex items-center gap-1 px-2 py-1 text-xs rounded-full transition-colors";
  
  const variantClasses = {
    default: isSelected 
      ? "bg-blue-100 text-blue-800 border border-blue-300" 
      : "bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200",
    mapped: "bg-green-100 text-green-800 border border-green-300",
    filter: "bg-blue-500 text-white border border-blue-600"
  };

  const handleClick = () => {
    if (isInteractive && onSelect) {
      onSelect(tag);
    }
  };

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onRemove) {
      onRemove(tag);
    }
  };

  return (
    <span 
      className={cn(
        baseClasses,
        variantClasses[variant],
        isInteractive && "cursor-pointer"
      )}
      onClick={handleClick}
      title={tag}
    >
      <span className="truncate max-w-[200px]">{tag}</span>
      {variant === 'filter' && onRemove && (
        <button
          onClick={handleRemove}
          className="ml-1 hover:bg-blue-600 rounded-full p-0.5"
        >
          <X size={12} />
        </button>
      )}
    </span>
  );
}
