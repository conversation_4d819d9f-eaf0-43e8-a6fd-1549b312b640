'use client';

import { useState, useEffect } from 'react';
import { Search, Filter, X } from 'lucide-react';
import { TagChip } from './TagChip';
import { cn } from '@/lib/utils';

interface SearchAndFilterProps {
  onSearch: (query: string) => void;
  onFilterChange: (filters: FilterState) => void;
  availableTags: string[];
  isLoading?: boolean;
}

export interface FilterState {
  search: string;
  mappingStatus: string;
  selectedTags: string[];
}

export function SearchAndFilter({ 
  onSearch, 
  onFilterChange, 
  availableTags,
  isLoading = false 
}: SearchAndFilterProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [mappingStatus, setMappingStatus] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [tagSearchQuery, setTagSearchQuery] = useState('');

  const mappingStatuses = [
    'Mapped',
    'Not Mapped',
    'In Progress',
    'Pending Review'
  ];

  const filteredTags = availableTags.filter(tag =>
    tag.toLowerCase().includes(tagSearchQuery.toLowerCase())
  ).slice(0, 50); // Limit to 50 tags for performance

  useEffect(() => {
    const filters: FilterState = {
      search: searchQuery,
      mappingStatus,
      selectedTags
    };
    onFilterChange(filters);
  }, [searchQuery, mappingStatus, selectedTags, onFilterChange]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    onSearch(query);
  };

  const handleTagSelect = (tag: string) => {
    if (!selectedTags.includes(tag)) {
      setSelectedTags([...selectedTags, tag]);
    }
  };

  const handleTagRemove = (tag: string) => {
    setSelectedTags(selectedTags.filter(t => t !== tag));
  };

  const clearAllFilters = () => {
    setSearchQuery('');
    setMappingStatus('');
    setSelectedTags([]);
    setTagSearchQuery('');
  };

  const hasActiveFilters = searchQuery || mappingStatus || selectedTags.length > 0;

  return (
    <div className="space-y-4 p-4 bg-white border rounded-lg shadow-sm">
      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
        <input
          type="text"
          placeholder="Search across all columns..."
          value={searchQuery}
          onChange={handleSearchChange}
          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          disabled={isLoading}
        />
      </div>

      {/* Filter Toggle */}
      <div className="flex items-center justify-between">
        <button
          onClick={() => setShowFilters(!showFilters)}
          className={cn(
            "flex items-center gap-2 px-3 py-2 rounded-lg border transition-colors",
            showFilters 
              ? "bg-blue-50 border-blue-300 text-blue-700" 
              : "bg-gray-50 border-gray-300 text-gray-700 hover:bg-gray-100"
          )}
        >
          <Filter size={16} />
          Advanced Filters
          {hasActiveFilters && (
            <span className="bg-blue-500 text-white text-xs rounded-full px-2 py-0.5">
              {(mappingStatus ? 1 : 0) + selectedTags.length}
            </span>
          )}
        </button>

        {hasActiveFilters && (
          <button
            onClick={clearAllFilters}
            className="flex items-center gap-1 px-3 py-2 text-sm text-gray-600 hover:text-gray-800"
          >
            <X size={14} />
            Clear All
          </button>
        )}
      </div>

      {/* Advanced Filters */}
      {showFilters && (
        <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
          {/* Mapping Status Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Mapping Status
            </label>
            <select
              value={mappingStatus}
              onChange={(e) => setMappingStatus(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Statuses</option>
              {mappingStatuses.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
          </div>

          {/* Selected Tags */}
          {selectedTags.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Selected Tags ({selectedTags.length})
              </label>
              <div className="flex flex-wrap gap-2">
                {selectedTags.map(tag => (
                  <TagChip
                    key={tag}
                    tag={tag}
                    variant="filter"
                    onRemove={handleTagRemove}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Tag Search and Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Search and Select Tags
            </label>
            <input
              type="text"
              placeholder="Search tags..."
              value={tagSearchQuery}
              onChange={(e) => setTagSearchQuery(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 mb-3"
            />
            
            <div className="max-h-40 overflow-y-auto border border-gray-200 rounded-lg p-2">
              <div className="flex flex-wrap gap-1">
                {filteredTags.map(tag => (
                  <TagChip
                    key={tag}
                    tag={tag}
                    isSelected={selectedTags.includes(tag)}
                    onSelect={handleTagSelect}
                    variant={selectedTags.includes(tag) ? 'mapped' : 'default'}
                  />
                ))}
              </div>
              {filteredTags.length === 0 && tagSearchQuery && (
                <p className="text-gray-500 text-sm text-center py-4">
                  No tags found matching "{tagSearchQuery}"
                </p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
