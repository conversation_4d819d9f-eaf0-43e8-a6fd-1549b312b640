'use client';

import { useState, useMemo } from 'react';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, Eye, EyeOff } from 'lucide-react';
import { TagChip } from './TagChip';
import { extractTags, formatCellValue, truncateText, cn } from '@/lib/utils';

interface DataTableProps {
  data: any[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  isLoading?: boolean;
}

export function DataTable({
  data,
  total,
  page,
  pageSize,
  totalPages,
  onPageChange,
  onPageSizeChange,
  isLoading = false
}: DataTableProps) {
  const [hiddenColumns, setHiddenColumns] = useState<Set<string>>(new Set());
  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set());

  const columns = useMemo(() => {
    if (data.length === 0) return [];
    return Object.keys(data[0]).filter(key => key !== 'id');
  }, [data]);

  const visibleColumns = columns.filter(col => !hiddenColumns.has(col));

  const tagColumns = [
    'Auto Selection Packages',
    'Documents',
    'Auto Selection Documents'
  ];

  const toggleColumnVisibility = (column: string) => {
    const newHidden = new Set(hiddenColumns);
    if (newHidden.has(column)) {
      newHidden.delete(column);
    } else {
      newHidden.add(column);
    }
    setHiddenColumns(newHidden);
  };

  const toggleRowExpansion = (rowId: number) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(rowId)) {
      newExpanded.delete(rowId);
    } else {
      newExpanded.add(rowId);
    }
    setExpandedRows(newExpanded);
  };

  const renderCellContent = (value: any, column: string, isExpanded: boolean = false) => {
    if (tagColumns.includes(column)) {
      const tags = extractTags(value);
      if (tags.length === 0) return <span className="text-gray-400">-</span>;
      
      if (isExpanded) {
        return (
          <div className="flex flex-wrap gap-1">
            {tags.map((tag, index) => (
              <TagChip key={index} tag={tag} variant="mapped" isInteractive={false} />
            ))}
          </div>
        );
      } else {
        return (
          <div className="flex flex-wrap gap-1">
            {tags.slice(0, 2).map((tag, index) => (
              <TagChip key={index} tag={truncateText(tag, 20)} variant="mapped" isInteractive={false} />
            ))}
            {tags.length > 2 && (
              <span className="text-xs text-gray-500 px-2 py-1">
                +{tags.length - 2} more
              </span>
            )}
          </div>
        );
      }
    }

    const formattedValue = formatCellValue(value);
    if (!formattedValue) return <span className="text-gray-400">-</span>;

    if (isExpanded || formattedValue.length <= 50) {
      return <span>{formattedValue}</span>;
    }

    return <span title={formattedValue}>{truncateText(formattedValue)}</span>;
  };

  const pageSizeOptions = [25, 50, 100, 200];

  return (
    <div className="space-y-4">
      {/* Column Visibility Controls */}
      <div className="flex flex-wrap gap-2 p-4 bg-gray-50 rounded-lg">
        <span className="text-sm font-medium text-gray-700 mr-2">Show/Hide Columns:</span>
        {columns.map(column => (
          <button
            key={column}
            onClick={() => toggleColumnVisibility(column)}
            className={cn(
              "flex items-center gap-1 px-2 py-1 text-xs rounded border transition-colors",
              hiddenColumns.has(column)
                ? "bg-gray-200 text-gray-500 border-gray-300"
                : "bg-blue-100 text-blue-700 border-blue-300"
            )}
          >
            {hiddenColumns.has(column) ? <EyeOff size={12} /> : <Eye size={12} />}
            {truncateText(column, 20)}
          </button>
        ))}
      </div>

      {/* Table */}
      <div className="border rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  #
                </th>
                {visibleColumns.map(column => (
                  <th
                    key={column}
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[150px]"
                  >
                    {column}
                  </th>
                ))}
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {isLoading ? (
                <tr>
                  <td colSpan={visibleColumns.length + 2} className="px-4 py-8 text-center">
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                      <span className="ml-2 text-gray-500">Loading...</span>
                    </div>
                  </td>
                </tr>
              ) : data.length === 0 ? (
                <tr>
                  <td colSpan={visibleColumns.length + 2} className="px-4 py-8 text-center text-gray-500">
                    No data found
                  </td>
                </tr>
              ) : (
                data.map((row, index) => {
                  const rowId = row.id || index;
                  const isExpanded = expandedRows.has(rowId);
                  
                  return (
                    <tr key={rowId} className="hover:bg-gray-50">
                      <td className="px-4 py-3 text-sm text-gray-900">
                        {(page - 1) * pageSize + index + 1}
                      </td>
                      {visibleColumns.map(column => (
                        <td key={column} className="px-4 py-3 text-sm text-gray-900 max-w-xs">
                          {renderCellContent(row[column], column, isExpanded)}
                        </td>
                      ))}
                      <td className="px-4 py-3 text-sm">
                        <button
                          onClick={() => toggleRowExpansion(rowId)}
                          className="text-blue-600 hover:text-blue-800 text-xs"
                        >
                          {isExpanded ? 'Collapse' : 'Expand'}
                        </button>
                      </td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between px-4 py-3 bg-white border rounded-lg">
        <div className="flex items-center gap-4">
          <span className="text-sm text-gray-700">
            Showing {Math.min((page - 1) * pageSize + 1, total)} to {Math.min(page * pageSize, total)} of {total} results
          </span>
          
          <select
            value={pageSize}
            onChange={(e) => onPageSizeChange(Number(e.target.value))}
            className="px-2 py-1 border border-gray-300 rounded text-sm"
          >
            {pageSizeOptions.map(size => (
              <option key={size} value={size}>{size} per page</option>
            ))}
          </select>
        </div>

        <div className="flex items-center gap-2">
          <button
            onClick={() => onPageChange(1)}
            disabled={page === 1}
            className="p-1 rounded border disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            <ChevronsLeft size={16} />
          </button>
          
          <button
            onClick={() => onPageChange(page - 1)}
            disabled={page === 1}
            className="p-1 rounded border disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            <ChevronLeft size={16} />
          </button>

          <span className="px-3 py-1 text-sm">
            Page {page} of {totalPages}
          </span>

          <button
            onClick={() => onPageChange(page + 1)}
            disabled={page === totalPages}
            className="p-1 rounded border disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            <ChevronRight size={16} />
          </button>
          
          <button
            onClick={() => onPageChange(totalPages)}
            disabled={page === totalPages}
            className="p-1 rounded border disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            <ChevronsRight size={16} />
          </button>
        </div>
      </div>
    </div>
  );
}
