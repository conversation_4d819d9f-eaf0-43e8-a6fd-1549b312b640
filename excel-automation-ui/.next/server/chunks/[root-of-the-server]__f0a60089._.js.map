{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/wk-excel-automation/excel-automation-ui/src/app/api/excel/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport * as XLSX from 'xlsx';\nimport path from 'path';\nimport fs from 'fs';\n\ninterface ExcelRow {\n  id: number;\n  [key: string]: any;\n}\n\ninterface FilterOptions {\n  search?: string;\n  mappingStatus?: string;\n  tags?: string[];\n  page?: number;\n  pageSize?: number;\n}\n\nlet cachedData: ExcelRow[] | null = null;\nlet allTags: Set<string> = new Set();\n\nfunction loadExcelData(): ExcelRow[] {\n  if (cachedData) return cachedData;\n\n  try {\n    const filePath = path.join(process.cwd(), 'Summit_ExpereMapping_v15 02.08.2024 (6).xlsx');\n\n    // Check if file exists\n    if (!fs.existsSync(filePath)) {\n      console.error('File not found at:', filePath);\n      return [];\n    }\n\n    console.log('Reading file from:', filePath);\n\n    // Read file as buffer first\n    const fileBuffer = fs.readFileSync(filePath);\n    const workbook = XLSX.read(fileBuffer, { type: 'buffer' });\n    const sheetName = workbook.SheetNames[0];\n    const worksheet = workbook.Sheets[sheetName];\n    const jsonData = XLSX.utils.sheet_to_json(worksheet);\n\n    cachedData = jsonData.map((row: any, index: number) => ({\n      id: index + 1,\n      ...row\n    }));\n\n    // Extract all unique tags from relevant columns\n    const tagColumns = [\n      'Auto Selection Packages',\n      'Documents', \n      'Auto Selection Documents'\n    ];\n\n    cachedData.forEach(row => {\n      tagColumns.forEach(col => {\n        if (row[col] && typeof row[col] === 'string') {\n          const tags = row[col].split('\\n').filter((tag: string) => tag.trim());\n          tags.forEach((tag: string) => allTags.add(tag.trim()));\n        }\n      });\n    });\n\n    return cachedData;\n  } catch (error) {\n    console.error('Error loading Excel file:', error);\n    return [];\n  }\n}\n\nfunction filterData(data: ExcelRow[], filters: FilterOptions): ExcelRow[] {\n  let filtered = [...data];\n\n  // Search filter\n  if (filters.search) {\n    const searchTerm = filters.search.toLowerCase();\n    filtered = filtered.filter(row => \n      Object.values(row).some(value => \n        value && value.toString().toLowerCase().includes(searchTerm)\n      )\n    );\n  }\n\n  // Mapping status filter\n  if (filters.mappingStatus) {\n    filtered = filtered.filter(row => \n      row['Mapping Status'] === filters.mappingStatus\n    );\n  }\n\n  // Tags filter\n  if (filters.tags && filters.tags.length > 0) {\n    filtered = filtered.filter(row => {\n      const tagColumns = ['Auto Selection Packages', 'Documents', 'Auto Selection Documents'];\n      return tagColumns.some(col => {\n        if (row[col] && typeof row[col] === 'string') {\n          const rowTags = row[col].split('\\n').map((tag: string) => tag.trim());\n          return filters.tags!.some(filterTag => \n            rowTags.some(rowTag => rowTag.includes(filterTag))\n          );\n        }\n        return false;\n      });\n    });\n  }\n\n  return filtered;\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const search = searchParams.get('search') || '';\n    const mappingStatus = searchParams.get('mappingStatus') || '';\n    const tags = searchParams.get('tags')?.split(',').filter(Boolean) || [];\n    const page = parseInt(searchParams.get('page') || '1');\n    const pageSize = parseInt(searchParams.get('pageSize') || '50');\n\n    const data = loadExcelData();\n    const filtered = filterData(data, { search, mappingStatus, tags });\n    \n    // Pagination\n    const startIndex = (page - 1) * pageSize;\n    const endIndex = startIndex + pageSize;\n    const paginatedData = filtered.slice(startIndex, endIndex);\n\n    return NextResponse.json({\n      data: paginatedData,\n      total: filtered.length,\n      page,\n      pageSize,\n      totalPages: Math.ceil(filtered.length / pageSize)\n    });\n  } catch (error) {\n    console.error('API Error:', error);\n    return NextResponse.json({ error: 'Failed to process data' }, { status: 500 });\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { action, data: requestData } = body;\n\n    if (action === 'getTags') {\n      const data = loadExcelData();\n      return NextResponse.json({ tags: Array.from(allTags).sort() });\n    }\n\n    if (action === 'getColumns') {\n      const data = loadExcelData();\n      if (data.length > 0) {\n        const columns = Object.keys(data[0]).filter(key => key !== 'id');\n        return NextResponse.json({ columns });\n      }\n      return NextResponse.json({ columns: [] });\n    }\n\n    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });\n  } catch (error) {\n    console.error('API Error:', error);\n    return NextResponse.json({ error: 'Failed to process request' }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;;;;;AAeA,IAAI,aAAgC;AACpC,IAAI,UAAuB,IAAI;AAE/B,SAAS;IACP,IAAI,YAAY,OAAO;IAEvB,IAAI;QACF,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;QAE1C,uBAAuB;QACvB,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,WAAW;YAC5B,QAAQ,KAAK,CAAC,sBAAsB;YACpC,OAAO,EAAE;QACX;QAEA,QAAQ,GAAG,CAAC,sBAAsB;QAElC,4BAA4B;QAC5B,MAAM,aAAa,wGAAE,CAAC,YAAY,CAAC;QACnC,MAAM,WAAW,uIAAS,CAAC,YAAY;YAAE,MAAM;QAAS;QACxD,MAAM,YAAY,SAAS,UAAU,CAAC,EAAE;QACxC,MAAM,YAAY,SAAS,MAAM,CAAC,UAAU;QAC5C,MAAM,WAAW,wIAAU,CAAC,aAAa,CAAC;QAE1C,aAAa,SAAS,GAAG,CAAC,CAAC,KAAU,QAAkB,CAAC;gBACtD,IAAI,QAAQ;gBACZ,GAAG,GAAG;YACR,CAAC;QAED,gDAAgD;QAChD,MAAM,aAAa;YACjB;YACA;YACA;SACD;QAED,WAAW,OAAO,CAAC,CAAA;YACjB,WAAW,OAAO,CAAC,CAAA;gBACjB,IAAI,GAAG,CAAC,IAAI,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,UAAU;oBAC5C,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,MAAM,CAAC,CAAC,MAAgB,IAAI,IAAI;oBAClE,KAAK,OAAO,CAAC,CAAC,MAAgB,QAAQ,GAAG,CAAC,IAAI,IAAI;gBACpD;YACF;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,EAAE;IACX;AACF;AAEA,SAAS,WAAW,IAAgB,EAAE,OAAsB;IAC1D,IAAI,WAAW;WAAI;KAAK;IAExB,gBAAgB;IAChB,IAAI,QAAQ,MAAM,EAAE;QAClB,MAAM,aAAa,QAAQ,MAAM,CAAC,WAAW;QAC7C,WAAW,SAAS,MAAM,CAAC,CAAA,MACzB,OAAO,MAAM,CAAC,KAAK,IAAI,CAAC,CAAA,QACtB,SAAS,MAAM,QAAQ,GAAG,WAAW,GAAG,QAAQ,CAAC;IAGvD;IAEA,wBAAwB;IACxB,IAAI,QAAQ,aAAa,EAAE;QACzB,WAAW,SAAS,MAAM,CAAC,CAAA,MACzB,GAAG,CAAC,iBAAiB,KAAK,QAAQ,aAAa;IAEnD;IAEA,cAAc;IACd,IAAI,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,GAAG;QAC3C,WAAW,SAAS,MAAM,CAAC,CAAA;YACzB,MAAM,aAAa;gBAAC;gBAA2B;gBAAa;aAA2B;YACvF,OAAO,WAAW,IAAI,CAAC,CAAA;gBACrB,IAAI,GAAG,CAAC,IAAI,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,UAAU;oBAC5C,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,MAAgB,IAAI,IAAI;oBAClE,OAAO,QAAQ,IAAI,CAAE,IAAI,CAAC,CAAA,YACxB,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,QAAQ,CAAC;gBAE3C;gBACA,OAAO;YACT;QACF;IACF;IAEA,OAAO;AACT;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,gBAAgB,aAAa,GAAG,CAAC,oBAAoB;QAC3D,MAAM,OAAO,aAAa,GAAG,CAAC,SAAS,MAAM,KAAK,OAAO,YAAY,EAAE;QACvE,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,WAAW,SAAS,aAAa,GAAG,CAAC,eAAe;QAE1D,MAAM,OAAO;QACb,MAAM,WAAW,WAAW,MAAM;YAAE;YAAQ;YAAe;QAAK;QAEhE,aAAa;QACb,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;QAChC,MAAM,WAAW,aAAa;QAC9B,MAAM,gBAAgB,SAAS,KAAK,CAAC,YAAY;QAEjD,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,MAAM;YACN,OAAO,SAAS,MAAM;YACtB;YACA;YACA,YAAY,KAAK,IAAI,CAAC,SAAS,MAAM,GAAG;QAC1C;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,gJAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAyB,GAAG;YAAE,QAAQ;QAAI;IAC9E;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG;QAEtC,IAAI,WAAW,WAAW;YACxB,MAAM,OAAO;YACb,OAAO,gJAAY,CAAC,IAAI,CAAC;gBAAE,MAAM,MAAM,IAAI,CAAC,SAAS,IAAI;YAAG;QAC9D;QAEA,IAAI,WAAW,cAAc;YAC3B,MAAM,OAAO;YACb,IAAI,KAAK,MAAM,GAAG,GAAG;gBACnB,MAAM,UAAU,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAA,MAAO,QAAQ;gBAC3D,OAAO,gJAAY,CAAC,IAAI,CAAC;oBAAE;gBAAQ;YACrC;YACA,OAAO,gJAAY,CAAC,IAAI,CAAC;gBAAE,SAAS,EAAE;YAAC;QACzC;QAEA,OAAO,gJAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAiB,GAAG;YAAE,QAAQ;QAAI;IACtE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,gJAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAA4B,GAAG;YAAE,QAAQ;QAAI;IACjF;AACF", "debugId": null}}]}