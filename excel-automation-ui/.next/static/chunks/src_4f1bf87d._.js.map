{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/wk-excel-automation/excel-automation-ui/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function extractTags(value: string | null | undefined): string[] {\n  if (!value || typeof value !== 'string') return [];\n  return value.split('\\n').map(tag => tag.trim()).filter(Boolean);\n}\n\nexport function formatCellValue(value: any): string {\n  if (value === null || value === undefined) return '';\n  if (typeof value === 'boolean') return value ? 'Yes' : 'No';\n  if (typeof value === 'number') return value.toString();\n  return value.toString();\n}\n\nexport function truncateText(text: string, maxLength: number = 50): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yKAAO,EAAC,IAAA,gJAAI,EAAC;AACtB;AAEO,SAAS,YAAY,KAAgC;IAC1D,IAAI,CAAC,SAAS,OAAO,UAAU,UAAU,OAAO,EAAE;IAClD,OAAO,MAAM,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IAAI,MAAM,CAAC;AACzD;AAEO,SAAS,gBAAgB,KAAU;IACxC,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;IAClD,IAAI,OAAO,UAAU,WAAW,OAAO,QAAQ,QAAQ;IACvD,IAAI,OAAO,UAAU,UAAU,OAAO,MAAM,QAAQ;IACpD,OAAO,MAAM,QAAQ;AACvB;AAEO,SAAS,aAAa,IAAY;QAAE,YAAA,iEAAoB;IAC7D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/wk-excel-automation/excel-automation-ui/src/components/TagChip.tsx"], "sourcesContent": ["'use client';\n\nimport { X } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface TagChipProps {\n  tag: string;\n  isSelected?: boolean;\n  isInteractive?: boolean;\n  onSelect?: (tag: string) => void;\n  onRemove?: (tag: string) => void;\n  variant?: 'default' | 'mapped' | 'filter';\n}\n\nexport function TagChip({ \n  tag, \n  isSelected = false, \n  isInteractive = true,\n  onSelect, \n  onRemove,\n  variant = 'default'\n}: TagChipProps) {\n  const baseClasses = \"inline-flex items-center gap-1 px-2 py-1 text-xs rounded-full transition-colors\";\n  \n  const variantClasses = {\n    default: isSelected \n      ? \"bg-blue-100 text-blue-800 border border-blue-300\" \n      : \"bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200\",\n    mapped: \"bg-green-100 text-green-800 border border-green-300\",\n    filter: \"bg-blue-500 text-white border border-blue-600\"\n  };\n\n  const handleClick = () => {\n    if (isInteractive && onSelect) {\n      onSelect(tag);\n    }\n  };\n\n  const handleRemove = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    if (onRemove) {\n      onRemove(tag);\n    }\n  };\n\n  return (\n    <span \n      className={cn(\n        baseClasses,\n        variantClasses[variant],\n        isInteractive && \"cursor-pointer\"\n      )}\n      onClick={handleClick}\n      title={tag}\n    >\n      <span className=\"truncate max-w-[200px]\">{tag}</span>\n      {variant === 'filter' && onRemove && (\n        <button\n          onClick={handleRemove}\n          className=\"ml-1 hover:bg-blue-600 rounded-full p-0.5\"\n        >\n          <X size={12} />\n        </button>\n      )}\n    </span>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAcO,SAAS,QAAQ,KAOT;QAPS,EACtB,GAAG,EACH,aAAa,KAAK,EAClB,gBAAgB,IAAI,EACpB,QAAQ,EACR,QAAQ,EACR,UAAU,SAAS,EACN,GAPS;IAQtB,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS,aACL,qDACA;QACJ,QAAQ;QACR,QAAQ;IACV;IAEA,MAAM,cAAc;QAClB,IAAI,iBAAiB,UAAU;YAC7B,SAAS;QACX;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,eAAe;QACjB,IAAI,UAAU;YACZ,SAAS;QACX;IACF;IAEA,qBACE,6LAAC;QACC,WAAW,IAAA,4HAAE,EACX,aACA,cAAc,CAAC,QAAQ,EACvB,iBAAiB;QAEnB,SAAS;QACT,OAAO;;0BAEP,6LAAC;gBAAK,WAAU;0BAA0B;;;;;;YACzC,YAAY,YAAY,0BACvB,6LAAC;gBACC,SAAS;gBACT,WAAU;0BAEV,cAAA,6LAAC,oMAAC;oBAAC,MAAM;;;;;;;;;;;;;;;;;AAKnB;KApDgB", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/wk-excel-automation/excel-automation-ui/src/components/SearchAndFilter.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Search, Filter, X } from 'lucide-react';\nimport { TagChip } from './TagChip';\nimport { cn } from '@/lib/utils';\n\ninterface SearchAndFilterProps {\n  onSearch: (query: string) => void;\n  onFilterChange: (filters: FilterState) => void;\n  availableTags: string[];\n  isLoading?: boolean;\n}\n\nexport interface FilterState {\n  search: string;\n  mappingStatus: string;\n  selectedTags: string[];\n}\n\nexport function SearchAndFilter({ \n  onSearch, \n  onFilterChange, \n  availableTags,\n  isLoading = false \n}: SearchAndFilterProps) {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [mappingStatus, setMappingStatus] = useState('');\n  const [selectedTags, setSelectedTags] = useState<string[]>([]);\n  const [tagSearchQuery, setTagSearchQuery] = useState('');\n\n  const mappingStatuses = [\n    'Mapped',\n    'Not Mapped',\n    'In Progress',\n    'Pending Review'\n  ];\n\n  const filteredTags = availableTags.filter(tag =>\n    tag.toLowerCase().includes(tagSearchQuery.toLowerCase())\n  ).slice(0, 50); // Limit to 50 tags for performance\n\n  useEffect(() => {\n    const filters: FilterState = {\n      search: searchQuery,\n      mappingStatus,\n      selectedTags\n    };\n    onFilterChange(filters);\n  }, [searchQuery, mappingStatus, selectedTags, onFilterChange]);\n\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const query = e.target.value;\n    setSearchQuery(query);\n    onSearch(query);\n  };\n\n  const handleTagSelect = (tag: string) => {\n    if (!selectedTags.includes(tag)) {\n      setSelectedTags([...selectedTags, tag]);\n    }\n  };\n\n  const handleTagRemove = (tag: string) => {\n    setSelectedTags(selectedTags.filter(t => t !== tag));\n  };\n\n  const clearAllFilters = () => {\n    setSearchQuery('');\n    setMappingStatus('');\n    setSelectedTags([]);\n    setTagSearchQuery('');\n  };\n\n  const hasActiveFilters = searchQuery || mappingStatus || selectedTags.length > 0;\n\n  return (\n    <div className=\"space-y-4 p-4 bg-white border rounded-lg shadow-sm\">\n      {/* Search Bar */}\n      <div className=\"relative\">\n        <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" size={20} />\n        <input\n          type=\"text\"\n          placeholder=\"Search across all columns...\"\n          value={searchQuery}\n          onChange={handleSearchChange}\n          className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          disabled={isLoading}\n        />\n      </div>\n\n      {/* Filter Toggle */}\n      <div className=\"flex items-center justify-between\">\n        <button\n          onClick={() => setShowFilters(!showFilters)}\n          className={cn(\n            \"flex items-center gap-2 px-3 py-2 rounded-lg border transition-colors\",\n            showFilters \n              ? \"bg-blue-50 border-blue-300 text-blue-700\" \n              : \"bg-gray-50 border-gray-300 text-gray-700 hover:bg-gray-100\"\n          )}\n        >\n          <Filter size={16} />\n          Advanced Filters\n          {hasActiveFilters && (\n            <span className=\"bg-blue-500 text-white text-xs rounded-full px-2 py-0.5\">\n              {(mappingStatus ? 1 : 0) + selectedTags.length}\n            </span>\n          )}\n        </button>\n\n        {hasActiveFilters && (\n          <button\n            onClick={clearAllFilters}\n            className=\"flex items-center gap-1 px-3 py-2 text-sm text-gray-600 hover:text-gray-800\"\n          >\n            <X size={14} />\n            Clear All\n          </button>\n        )}\n      </div>\n\n      {/* Advanced Filters */}\n      {showFilters && (\n        <div className=\"space-y-4 p-4 bg-gray-50 rounded-lg\">\n          {/* Mapping Status Filter */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Mapping Status\n            </label>\n            <select\n              value={mappingStatus}\n              onChange={(e) => setMappingStatus(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"\">All Statuses</option>\n              {mappingStatuses.map(status => (\n                <option key={status} value={status}>{status}</option>\n              ))}\n            </select>\n          </div>\n\n          {/* Selected Tags */}\n          {selectedTags.length > 0 && (\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Selected Tags ({selectedTags.length})\n              </label>\n              <div className=\"flex flex-wrap gap-2\">\n                {selectedTags.map(tag => (\n                  <TagChip\n                    key={tag}\n                    tag={tag}\n                    variant=\"filter\"\n                    onRemove={handleTagRemove}\n                  />\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* Tag Search and Selection */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Search and Select Tags\n            </label>\n            <input\n              type=\"text\"\n              placeholder=\"Search tags...\"\n              value={tagSearchQuery}\n              onChange={(e) => setTagSearchQuery(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 mb-3\"\n            />\n            \n            <div className=\"max-h-40 overflow-y-auto border border-gray-200 rounded-lg p-2\">\n              <div className=\"flex flex-wrap gap-1\">\n                {filteredTags.map(tag => (\n                  <TagChip\n                    key={tag}\n                    tag={tag}\n                    isSelected={selectedTags.includes(tag)}\n                    onSelect={handleTagSelect}\n                    variant={selectedTags.includes(tag) ? 'mapped' : 'default'}\n                  />\n                ))}\n              </div>\n              {filteredTags.length === 0 && tagSearchQuery && (\n                <p className=\"text-gray-500 text-sm text-center py-4\">\n                  No tags found matching \"{tagSearchQuery}\"\n                </p>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;;;AALA;;;;;AAoBO,SAAS,gBAAgB,KAKT;QALS,EAC9B,QAAQ,EACR,cAAc,EACd,aAAa,EACb,YAAY,KAAK,EACI,GALS;;IAM9B,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAC;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAW,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,yKAAQ,EAAC;IAErD,MAAM,kBAAkB;QACtB;QACA;QACA;QACA;KACD;IAED,MAAM,eAAe,cAAc,MAAM,CAAC,CAAA,MACxC,IAAI,WAAW,GAAG,QAAQ,CAAC,eAAe,WAAW,KACrD,KAAK,CAAC,GAAG,KAAK,mCAAmC;IAEnD,IAAA,0KAAS;qCAAC;YACR,MAAM,UAAuB;gBAC3B,QAAQ;gBACR;gBACA;YACF;YACA,eAAe;QACjB;oCAAG;QAAC;QAAa;QAAe;QAAc;KAAe;IAE7D,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,eAAe;QACf,SAAS;IACX;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,aAAa,QAAQ,CAAC,MAAM;YAC/B,gBAAgB;mBAAI;gBAAc;aAAI;QACxC;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,gBAAgB,aAAa,MAAM,CAAC,CAAA,IAAK,MAAM;IACjD;IAEA,MAAM,kBAAkB;QACtB,eAAe;QACf,iBAAiB;QACjB,gBAAgB,EAAE;QAClB,kBAAkB;IACpB;IAEA,MAAM,mBAAmB,eAAe,iBAAiB,aAAa,MAAM,GAAG;IAE/E,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mNAAM;wBAAC,WAAU;wBAAmE,MAAM;;;;;;kCAC3F,6LAAC;wBACC,MAAK;wBACL,aAAY;wBACZ,OAAO;wBACP,UAAU;wBACV,WAAU;wBACV,UAAU;;;;;;;;;;;;0BAKd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,eAAe,CAAC;wBAC/B,WAAW,IAAA,4HAAE,EACX,yEACA,cACI,6CACA;;0CAGN,6LAAC,mNAAM;gCAAC,MAAM;;;;;;4BAAM;4BAEnB,kCACC,6LAAC;gCAAK,WAAU;0CACb,CAAC,gBAAgB,IAAI,CAAC,IAAI,aAAa,MAAM;;;;;;;;;;;;oBAKnD,kCACC,6LAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,6LAAC,oMAAC;gCAAC,MAAM;;;;;;4BAAM;;;;;;;;;;;;;YAOpB,6BACC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gCAChD,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,gBAAgB,GAAG,CAAC,CAAA,uBACnB,6LAAC;4CAAoB,OAAO;sDAAS;2CAAxB;;;;;;;;;;;;;;;;;oBAMlB,aAAa,MAAM,GAAG,mBACrB,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;;oCAA+C;oCAC9C,aAAa,MAAM;oCAAC;;;;;;;0CAEtC,6LAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAA,oBAChB,6LAAC,2IAAO;wCAEN,KAAK;wCACL,SAAQ;wCACR,UAAU;uCAHL;;;;;;;;;;;;;;;;kCAWf,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gCACjD,WAAU;;;;;;0CAGZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAA,oBAChB,6LAAC,2IAAO;gDAEN,KAAK;gDACL,YAAY,aAAa,QAAQ,CAAC;gDAClC,UAAU;gDACV,SAAS,aAAa,QAAQ,CAAC,OAAO,WAAW;+CAJ5C;;;;;;;;;;oCAQV,aAAa,MAAM,KAAK,KAAK,gCAC5B,6LAAC;wCAAE,WAAU;;4CAAyC;4CAC3B;4CAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1D;GAlLgB;KAAA", "debugId": null}}, {"offset": {"line": 448, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/wk-excel-automation/excel-automation-ui/src/components/DataTable.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useMemo } from 'react';\nimport { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, Eye, EyeOff } from 'lucide-react';\nimport { TagChip } from './TagChip';\nimport { extractTags, formatCellValue, truncateText, cn } from '@/lib/utils';\n\ninterface DataTableProps {\n  data: any[];\n  total: number;\n  page: number;\n  pageSize: number;\n  totalPages: number;\n  onPageChange: (page: number) => void;\n  onPageSizeChange: (pageSize: number) => void;\n  isLoading?: boolean;\n}\n\nexport function DataTable({\n  data,\n  total,\n  page,\n  pageSize,\n  totalPages,\n  onPageChange,\n  onPageSizeChange,\n  isLoading = false\n}: DataTableProps) {\n  const [hiddenColumns, setHiddenColumns] = useState<Set<string>>(new Set());\n  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set());\n\n  const columns = useMemo(() => {\n    if (data.length === 0) return [];\n    return Object.keys(data[0]).filter(key => key !== 'id');\n  }, [data]);\n\n  const visibleColumns = columns.filter(col => !hiddenColumns.has(col));\n\n  const tagColumns = [\n    'Auto Selection Packages',\n    'Documents',\n    'Auto Selection Documents'\n  ];\n\n  const toggleColumnVisibility = (column: string) => {\n    const newHidden = new Set(hiddenColumns);\n    if (newHidden.has(column)) {\n      newHidden.delete(column);\n    } else {\n      newHidden.add(column);\n    }\n    setHiddenColumns(newHidden);\n  };\n\n  const toggleRowExpansion = (rowId: number) => {\n    const newExpanded = new Set(expandedRows);\n    if (newExpanded.has(rowId)) {\n      newExpanded.delete(rowId);\n    } else {\n      newExpanded.add(rowId);\n    }\n    setExpandedRows(newExpanded);\n  };\n\n  const renderCellContent = (value: any, column: string, isExpanded: boolean = false) => {\n    if (tagColumns.includes(column)) {\n      const tags = extractTags(value);\n      if (tags.length === 0) return <span className=\"text-gray-400\">-</span>;\n      \n      if (isExpanded) {\n        return (\n          <div className=\"flex flex-wrap gap-1\">\n            {tags.map((tag, index) => (\n              <TagChip key={index} tag={tag} variant=\"mapped\" isInteractive={false} />\n            ))}\n          </div>\n        );\n      } else {\n        return (\n          <div className=\"flex flex-wrap gap-1\">\n            {tags.slice(0, 2).map((tag, index) => (\n              <TagChip key={index} tag={truncateText(tag, 20)} variant=\"mapped\" isInteractive={false} />\n            ))}\n            {tags.length > 2 && (\n              <span className=\"text-xs text-gray-500 px-2 py-1\">\n                +{tags.length - 2} more\n              </span>\n            )}\n          </div>\n        );\n      }\n    }\n\n    const formattedValue = formatCellValue(value);\n    if (!formattedValue) return <span className=\"text-gray-400\">-</span>;\n\n    if (isExpanded || formattedValue.length <= 50) {\n      return <span>{formattedValue}</span>;\n    }\n\n    return <span title={formattedValue}>{truncateText(formattedValue)}</span>;\n  };\n\n  const pageSizeOptions = [25, 50, 100, 200];\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Column Visibility Controls */}\n      <div className=\"flex flex-wrap gap-2 p-4 bg-gray-50 rounded-lg\">\n        <span className=\"text-sm font-medium text-gray-700 mr-2\">Show/Hide Columns:</span>\n        {columns.map(column => (\n          <button\n            key={column}\n            onClick={() => toggleColumnVisibility(column)}\n            className={cn(\n              \"flex items-center gap-1 px-2 py-1 text-xs rounded border transition-colors\",\n              hiddenColumns.has(column)\n                ? \"bg-gray-200 text-gray-500 border-gray-300\"\n                : \"bg-blue-100 text-blue-700 border-blue-300\"\n            )}\n          >\n            {hiddenColumns.has(column) ? <EyeOff size={12} /> : <Eye size={12} />}\n            {truncateText(column, 20)}\n          </button>\n        ))}\n      </div>\n\n      {/* Table */}\n      <div className=\"border rounded-lg overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full\">\n            <thead className=\"bg-gray-50 border-b\">\n              <tr>\n                <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  #\n                </th>\n                {visibleColumns.map(column => (\n                  <th\n                    key={column}\n                    className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[150px]\"\n                  >\n                    {column}\n                  </th>\n                ))}\n                <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Actions\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {isLoading ? (\n                <tr>\n                  <td colSpan={visibleColumns.length + 2} className=\"px-4 py-8 text-center\">\n                    <div className=\"flex items-center justify-center\">\n                      <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500\"></div>\n                      <span className=\"ml-2 text-gray-500\">Loading...</span>\n                    </div>\n                  </td>\n                </tr>\n              ) : data.length === 0 ? (\n                <tr>\n                  <td colSpan={visibleColumns.length + 2} className=\"px-4 py-8 text-center text-gray-500\">\n                    No data found\n                  </td>\n                </tr>\n              ) : (\n                data.map((row, index) => {\n                  const rowId = row.id || index;\n                  const isExpanded = expandedRows.has(rowId);\n                  \n                  return (\n                    <tr key={rowId} className=\"hover:bg-gray-50\">\n                      <td className=\"px-4 py-3 text-sm text-gray-900\">\n                        {(page - 1) * pageSize + index + 1}\n                      </td>\n                      {visibleColumns.map(column => (\n                        <td key={column} className=\"px-4 py-3 text-sm text-gray-900 max-w-xs\">\n                          {renderCellContent(row[column], column, isExpanded)}\n                        </td>\n                      ))}\n                      <td className=\"px-4 py-3 text-sm\">\n                        <button\n                          onClick={() => toggleRowExpansion(rowId)}\n                          className=\"text-blue-600 hover:text-blue-800 text-xs\"\n                        >\n                          {isExpanded ? 'Collapse' : 'Expand'}\n                        </button>\n                      </td>\n                    </tr>\n                  );\n                })\n              )}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {/* Pagination */}\n      <div className=\"flex items-center justify-between px-4 py-3 bg-white border rounded-lg\">\n        <div className=\"flex items-center gap-4\">\n          <span className=\"text-sm text-gray-700\">\n            Showing {Math.min((page - 1) * pageSize + 1, total)} to {Math.min(page * pageSize, total)} of {total} results\n          </span>\n          \n          <select\n            value={pageSize}\n            onChange={(e) => onPageSizeChange(Number(e.target.value))}\n            className=\"px-2 py-1 border border-gray-300 rounded text-sm\"\n          >\n            {pageSizeOptions.map(size => (\n              <option key={size} value={size}>{size} per page</option>\n            ))}\n          </select>\n        </div>\n\n        <div className=\"flex items-center gap-2\">\n          <button\n            onClick={() => onPageChange(1)}\n            disabled={page === 1}\n            className=\"p-1 rounded border disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\"\n          >\n            <ChevronsLeft size={16} />\n          </button>\n          \n          <button\n            onClick={() => onPageChange(page - 1)}\n            disabled={page === 1}\n            className=\"p-1 rounded border disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\"\n          >\n            <ChevronLeft size={16} />\n          </button>\n\n          <span className=\"px-3 py-1 text-sm\">\n            Page {page} of {totalPages}\n          </span>\n\n          <button\n            onClick={() => onPageChange(page + 1)}\n            disabled={page === totalPages}\n            className=\"p-1 rounded border disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\"\n          >\n            <ChevronRight size={16} />\n          </button>\n          \n          <button\n            onClick={() => onPageChange(totalPages)}\n            disabled={page === totalPages}\n            className=\"p-1 rounded border disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\"\n          >\n            <ChevronsRight size={16} />\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AALA;;;;;AAkBO,SAAS,UAAU,KAST;QATS,EACxB,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,QAAQ,EACR,UAAU,EACV,YAAY,EACZ,gBAAgB,EAChB,YAAY,KAAK,EACF,GATS;;;IAUxB,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAc,IAAI;IACpE,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAc,IAAI;IAElE,MAAM,UAAU,IAAA,wKAAO;sCAAC;YACtB,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO,EAAE;YAChC,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM;8CAAC,CAAA,MAAO,QAAQ;;QACpD;qCAAG;QAAC;KAAK;IAET,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,MAAO,CAAC,cAAc,GAAG,CAAC;IAEhE,MAAM,aAAa;QACjB;QACA;QACA;KACD;IAED,MAAM,yBAAyB,CAAC;QAC9B,MAAM,YAAY,IAAI,IAAI;QAC1B,IAAI,UAAU,GAAG,CAAC,SAAS;YACzB,UAAU,MAAM,CAAC;QACnB,OAAO;YACL,UAAU,GAAG,CAAC;QAChB;QACA,iBAAiB;IACnB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,QAAQ;YAC1B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,gBAAgB;IAClB;IAEA,MAAM,oBAAoB,SAAC,OAAY;YAAgB,8EAAsB;QAC3E,IAAI,WAAW,QAAQ,CAAC,SAAS;YAC/B,MAAM,OAAO,IAAA,qIAAW,EAAC;YACzB,IAAI,KAAK,MAAM,KAAK,GAAG,qBAAO,6LAAC;gBAAK,WAAU;0BAAgB;;;;;;YAE9D,IAAI,YAAY;gBACd,qBACE,6LAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6LAAC,2IAAO;4BAAa,KAAK;4BAAK,SAAQ;4BAAS,eAAe;2BAAjD;;;;;;;;;;YAItB,OAAO;gBACL,qBACE,6LAAC;oBAAI,WAAU;;wBACZ,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC1B,6LAAC,2IAAO;gCAAa,KAAK,IAAA,sIAAY,EAAC,KAAK;gCAAK,SAAQ;gCAAS,eAAe;+BAAnE;;;;;wBAEf,KAAK,MAAM,GAAG,mBACb,6LAAC;4BAAK,WAAU;;gCAAkC;gCAC9C,KAAK,MAAM,GAAG;gCAAE;;;;;;;;;;;;;YAK5B;QACF;QAEA,MAAM,iBAAiB,IAAA,yIAAe,EAAC;QACvC,IAAI,CAAC,gBAAgB,qBAAO,6LAAC;YAAK,WAAU;sBAAgB;;;;;;QAE5D,IAAI,cAAc,eAAe,MAAM,IAAI,IAAI;YAC7C,qBAAO,6LAAC;0BAAM;;;;;;QAChB;QAEA,qBAAO,6LAAC;YAAK,OAAO;sBAAiB,IAAA,sIAAY,EAAC;;;;;;IACpD;IAEA,MAAM,kBAAkB;QAAC;QAAI;QAAI;QAAK;KAAI;IAE1C,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAyC;;;;;;oBACxD,QAAQ,GAAG,CAAC,CAAA,uBACX,6LAAC;4BAEC,SAAS,IAAM,uBAAuB;4BACtC,WAAW,IAAA,4HAAE,EACX,8EACA,cAAc,GAAG,CAAC,UACd,8CACA;;gCAGL,cAAc,GAAG,CAAC,wBAAU,6LAAC,uNAAM;oCAAC,MAAM;;;;;yDAAS,6LAAC,0MAAG;oCAAC,MAAM;;;;;;gCAC9D,IAAA,sIAAY,EAAC,QAAQ;;2BAVjB;;;;;;;;;;;0BAgBX,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAM,WAAU;;0CACf,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;wCAG9F,eAAe,GAAG,CAAC,CAAA,uBAClB,6LAAC;gDAEC,WAAU;0DAET;+CAHI;;;;;sDAMT,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;;;;;;;;;;;;0CAKnG,6LAAC;gCAAM,WAAU;0CACd,0BACC,6LAAC;8CACC,cAAA,6LAAC;wCAAG,SAAS,eAAe,MAAM,GAAG;wCAAG,WAAU;kDAChD,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAAqB;;;;;;;;;;;;;;;;;;;;;2CAIzC,KAAK,MAAM,KAAK,kBAClB,6LAAC;8CACC,cAAA,6LAAC;wCAAG,SAAS,eAAe,MAAM,GAAG;wCAAG,WAAU;kDAAsC;;;;;;;;;;2CAK1F,KAAK,GAAG,CAAC,CAAC,KAAK;oCACb,MAAM,QAAQ,IAAI,EAAE,IAAI;oCACxB,MAAM,aAAa,aAAa,GAAG,CAAC;oCAEpC,qBACE,6LAAC;wCAAe,WAAU;;0DACxB,6LAAC;gDAAG,WAAU;0DACX,CAAC,OAAO,CAAC,IAAI,WAAW,QAAQ;;;;;;4CAElC,eAAe,GAAG,CAAC,CAAA,uBAClB,6LAAC;oDAAgB,WAAU;8DACxB,kBAAkB,GAAG,CAAC,OAAO,EAAE,QAAQ;mDADjC;;;;;0DAIX,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDACC,SAAS,IAAM,mBAAmB;oDAClC,WAAU;8DAET,aAAa,aAAa;;;;;;;;;;;;uCAdxB;;;;;gCAmBb;;;;;;;;;;;;;;;;;;;;;;0BAQV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;;oCAAwB;oCAC7B,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,WAAW,GAAG;oCAAO;oCAAK,KAAK,GAAG,CAAC,OAAO,UAAU;oCAAO;oCAAK;oCAAM;;;;;;;0CAGvG,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,iBAAiB,OAAO,EAAE,MAAM,CAAC,KAAK;gCACvD,WAAU;0CAET,gBAAgB,GAAG,CAAC,CAAA,qBACnB,6LAAC;wCAAkB,OAAO;;4CAAO;4CAAK;;uCAAzB;;;;;;;;;;;;;;;;kCAKnB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,UAAU,SAAS;gCACnB,WAAU;0CAEV,cAAA,6LAAC,yOAAY;oCAAC,MAAM;;;;;;;;;;;0CAGtB,6LAAC;gCACC,SAAS,IAAM,aAAa,OAAO;gCACnC,UAAU,SAAS;gCACnB,WAAU;0CAEV,cAAA,6LAAC,sOAAW;oCAAC,MAAM;;;;;;;;;;;0CAGrB,6LAAC;gCAAK,WAAU;;oCAAoB;oCAC5B;oCAAK;oCAAK;;;;;;;0CAGlB,6LAAC;gCACC,SAAS,IAAM,aAAa,OAAO;gCACnC,UAAU,SAAS;gCACnB,WAAU;0CAEV,cAAA,6LAAC,yOAAY;oCAAC,MAAM;;;;;;;;;;;0CAGtB,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,UAAU,SAAS;gCACnB,WAAU;0CAEV,cAAA,6LAAC,4OAAa;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjC;GA7OgB;KAAA", "debugId": null}}, {"offset": {"line": 971, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/wk-excel-automation/excel-automation-ui/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useCallback } from 'react';\nimport { SearchAndFilter, FilterState } from '@/components/SearchAndFilter';\nimport { DataTable } from '@/components/DataTable';\nimport { Database, FileSpreadsheet } from 'lucide-react';\n\ninterface ApiResponse {\n  data: any[];\n  total: number;\n  page: number;\n  pageSize: number;\n  totalPages: number;\n}\n\nexport default function Home() {\n  const [data, setData] = useState<any[]>([]);\n  const [total, setTotal] = useState(0);\n  const [page, setPage] = useState(1);\n  const [pageSize, setPageSize] = useState(50);\n  const [totalPages, setTotalPages] = useState(0);\n  const [isLoading, setIsLoading] = useState(false);\n  const [availableTags, setAvailableTags] = useState<string[]>([]);\n  const [filters, setFilters] = useState<FilterState>({\n    search: '',\n    mappingStatus: '',\n    selectedTags: []\n  });\n\n  const fetchData = useCallback(async () => {\n    setIsLoading(true);\n    try {\n      const params = new URLSearchParams({\n        page: page.toString(),\n        pageSize: pageSize.toString(),\n        search: filters.search,\n        mappingStatus: filters.mappingStatus,\n        tags: filters.selectedTags.join(',')\n      });\n\n      const response = await fetch(`/api/excel?${params}`);\n      const result: ApiResponse = await response.json();\n\n      setData(result.data);\n      setTotal(result.total);\n      setTotalPages(result.totalPages);\n    } catch (error) {\n      console.error('Error fetching data:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [page, pageSize, filters]);\n\n  const fetchTags = useCallback(async () => {\n    try {\n      const response = await fetch('/api/excel', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ action: 'getTags' })\n      });\n      const result = await response.json();\n      setAvailableTags(result.tags || []);\n    } catch (error) {\n      console.error('Error fetching tags:', error);\n    }\n  }, []);\n\n  useEffect(() => {\n    fetchTags();\n  }, [fetchTags]);\n\n  useEffect(() => {\n    fetchData();\n  }, [fetchData]);\n\n  const handleSearch = (query: string) => {\n    setFilters(prev => ({ ...prev, search: query }));\n    setPage(1); // Reset to first page when searching\n  };\n\n  const handleFilterChange = (newFilters: FilterState) => {\n    setFilters(newFilters);\n    setPage(1); // Reset to first page when filtering\n  };\n\n  const handlePageChange = (newPage: number) => {\n    setPage(newPage);\n  };\n\n  const handlePageSizeChange = (newPageSize: number) => {\n    setPageSize(newPageSize);\n    setPage(1); // Reset to first page when changing page size\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex items-center gap-3\">\n            <FileSpreadsheet className=\"text-blue-600\" size={32} />\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">\n                Excel Data Management System\n              </h1>\n              <p className=\"text-gray-600\">\n                Search, filter, and manage your Excel data with interactive tags\n              </p>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        <div className=\"space-y-6\">\n          {/* Stats */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div className=\"bg-white p-4 rounded-lg shadow-sm border\">\n              <div className=\"flex items-center gap-2\">\n                <Database className=\"text-blue-600\" size={20} />\n                <div>\n                  <p className=\"text-sm text-gray-600\">Total Records</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{total.toLocaleString()}</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white p-4 rounded-lg shadow-sm border\">\n              <div className=\"flex items-center gap-2\">\n                <FileSpreadsheet className=\"text-green-600\" size={20} />\n                <div>\n                  <p className=\"text-sm text-gray-600\">Available Tags</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{availableTags.length.toLocaleString()}</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white p-4 rounded-lg shadow-sm border\">\n              <div className=\"flex items-center gap-2\">\n                <div className=\"w-5 h-5 bg-blue-500 rounded\"></div>\n                <div>\n                  <p className=\"text-sm text-gray-600\">Active Filters</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    {(filters.mappingStatus ? 1 : 0) + filters.selectedTags.length}\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Search and Filter */}\n          <SearchAndFilter\n            onSearch={handleSearch}\n            onFilterChange={handleFilterChange}\n            availableTags={availableTags}\n            isLoading={isLoading}\n          />\n\n          {/* Data Table */}\n          <DataTable\n            data={data}\n            total={total}\n            page={page}\n            pageSize={pageSize}\n            totalPages={totalPages}\n            onPageChange={handlePageChange}\n            onPageSizeChange={handlePageSizeChange}\n            isLoading={isLoading}\n          />\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;;;AALA;;;;;AAee,SAAS;;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,yKAAQ,EAAQ,EAAE;IAC1C,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAC;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,yKAAQ,EAAC;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAC;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAC;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAW,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAc;QAClD,QAAQ;QACR,eAAe;QACf,cAAc,EAAE;IAClB;IAEA,MAAM,YAAY,IAAA,4KAAW;uCAAC;YAC5B,aAAa;YACb,IAAI;gBACF,MAAM,SAAS,IAAI,gBAAgB;oBACjC,MAAM,KAAK,QAAQ;oBACnB,UAAU,SAAS,QAAQ;oBAC3B,QAAQ,QAAQ,MAAM;oBACtB,eAAe,QAAQ,aAAa;oBACpC,MAAM,QAAQ,YAAY,CAAC,IAAI,CAAC;gBAClC;gBAEA,MAAM,WAAW,MAAM,MAAM,AAAC,cAAoB,OAAP;gBAC3C,MAAM,SAAsB,MAAM,SAAS,IAAI;gBAE/C,QAAQ,OAAO,IAAI;gBACnB,SAAS,OAAO,KAAK;gBACrB,cAAc,OAAO,UAAU;YACjC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;YACxC,SAAU;gBACR,aAAa;YACf;QACF;sCAAG;QAAC;QAAM;QAAU;KAAQ;IAE5B,MAAM,YAAY,IAAA,4KAAW;uCAAC;YAC5B,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,cAAc;oBACzC,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;wBAAE,QAAQ;oBAAU;gBAC3C;gBACA,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,iBAAiB,OAAO,IAAI,IAAI,EAAE;YACpC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;YACxC;QACF;sCAAG,EAAE;IAEL,IAAA,0KAAS;0BAAC;YACR;QACF;yBAAG;QAAC;KAAU;IAEd,IAAA,0KAAS;0BAAC;YACR;QACF;yBAAG;QAAC;KAAU;IAEd,MAAM,eAAe,CAAC;QACpB,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,QAAQ;YAAM,CAAC;QAC9C,QAAQ,IAAI,qCAAqC;IACnD;IAEA,MAAM,qBAAqB,CAAC;QAC1B,WAAW;QACX,QAAQ,IAAI,qCAAqC;IACnD;IAEA,MAAM,mBAAmB,CAAC;QACxB,QAAQ;IACV;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY;QACZ,QAAQ,IAAI,8CAA8C;IAC5D;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,kPAAe;gCAAC,WAAU;gCAAgB,MAAM;;;;;;0CACjD,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDAGjD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrC,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yNAAQ;gDAAC,WAAU;gDAAgB,MAAM;;;;;;0DAC1C,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;kEACrC,6LAAC;wDAAE,WAAU;kEAAoC,MAAM,cAAc;;;;;;;;;;;;;;;;;;;;;;;8CAK3E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,kPAAe;gDAAC,WAAU;gDAAiB,MAAM;;;;;;0DAClD,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;kEACrC,6LAAC;wDAAE,WAAU;kEAAoC,cAAc,MAAM,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;8CAK1F,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;kEACrC,6LAAC;wDAAE,WAAU;kEACV,CAAC,QAAQ,aAAa,GAAG,IAAI,CAAC,IAAI,QAAQ,YAAY,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQxE,6LAAC,2JAAe;4BACd,UAAU;4BACV,gBAAgB;4BAChB,eAAe;4BACf,WAAW;;;;;;sCAIb,6LAAC,+IAAS;4BACR,MAAM;4BACN,OAAO;4BACP,MAAM;4BACN,UAAU;4BACV,YAAY;4BACZ,cAAc;4BACd,kBAAkB;4BAClB,WAAW;;;;;;;;;;;;;;;;;;;;;;;AAMvB;GA/JwB;KAAA", "debugId": null}}]}